-- RAG系统种子数据
-- 插入初始数据

-- 插入默认管理员用户
INSERT INTO users (id, email, password_hash, name, role, status, email_verified) VALUES
(
    gen_random_uuid(),
    '<EMAIL>',
    '$2b$10$rOzJqQZJQZJQZJQZJQZJQOzJqQZJQZJQZJQZJQZJQZJQZJQZJQZJQ', -- 密码: admin123
    '系统管理员',
    'admin',
    'active',
    true
),
(
    gen_random_uuid(),
    '<EMAIL>',
    '$2b$10$rOzJqQZJQZJQZJQZJQZJQOzJqQZJQZJQZJQZJQZJQZJQZJQZJQZJQ', -- 密码: demo123
    '演示用户',
    'user',
    'active',
    true
);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description, config_type) VALUES
-- 嵌入配置
('embedding.model', '"text-embedding-ada-002"', 'OpenAI嵌入模型', 'embedding'),
('embedding.dimension', '1536', '向量维度', 'embedding'),
('embedding.batch_size', '100', '批处理大小', 'embedding'),

-- 检索配置
('retrieval.default_top_k', '10', '默认检索数量', 'retrieval'),
('retrieval.default_threshold', '0.7', '默认相似度阈值', 'retrieval'),
('retrieval.max_top_k', '50', '最大检索数量', 'retrieval'),
('retrieval.strategy', '"hybrid"', '默认检索策略', 'retrieval'),

-- 生成配置
('generation.default_model', '"gpt-3.5-turbo"', '默认生成模型', 'generation'),
('generation.default_temperature', '0.7', '默认生成温度', 'generation'),
('generation.max_tokens', '2000', '最大token数', 'generation'),
('generation.stream_response', 'true', '是否流式响应', 'generation'),

-- 存储配置
('storage.max_file_size', '52428800', '最大文件大小(50MB)', 'storage'),
('storage.allowed_types', '["pdf", "docx", "txt", "md"]', '允许的文件类型', 'storage'),
('storage.retention_days', '365', '文件保留天数', 'storage'),

-- 安全配置
('security.jwt_expires_in', '"7d"', 'JWT过期时间', 'security'),
('security.refresh_expires_in', '"30d"', '刷新令牌过期时间', 'security'),
('security.rate_limit_window', '900000', '限流窗口(15分钟)', 'security'),
('security.rate_limit_max', '100', '限流最大请求数', 'security');

-- 插入文档分类
INSERT INTO documents (id, user_id, filename, title, description, file_type, file_size, file_hash, storage_path, status, processing_progress, category, tags) 
SELECT 
    gen_random_uuid(),
    u.id,
    'sample-document.pdf',
    '示例文档',
    '这是一个示例文档，用于演示系统功能',
    'pdf',
    1024000,
    'abc123def456',
    '/documents/sample-document.pdf',
    'completed',
    100,
    '技术文档',
    ARRAY['示例', '演示', '技术']
FROM users u WHERE u.email = '<EMAIL>';

-- 插入示例对话
INSERT INTO conversations (id, user_id, title, description, status, message_count)
SELECT 
    gen_random_uuid(),
    u.id,
    '欢迎对话',
    '系统欢迎对话，介绍基本功能',
    'active',
    2
FROM users u WHERE u.email = '<EMAIL>';

-- 插入示例消息
WITH demo_user AS (
    SELECT id FROM users WHERE email = '<EMAIL>'
),
demo_conversation AS (
    SELECT c.id FROM conversations c 
    JOIN demo_user u ON c.user_id = u.id 
    WHERE c.title = '欢迎对话'
)
INSERT INTO messages (id, conversation_id, role, content, metadata) VALUES
(
    gen_random_uuid(),
    (SELECT id FROM demo_conversation),
    'user',
    '你好，这个系统有什么功能？',
    '{"timestamp": "2024-01-15T10:00:00Z"}'
),
(
    gen_random_uuid(),
    (SELECT id FROM demo_conversation),
    'assistant',
    '您好！欢迎使用RAG智能问答系统。本系统主要功能包括：

1. **文档上传与管理**：支持PDF、Word、文本等多种格式文档上传
2. **智能问答**：基于您上传的文档内容进行智能问答
3. **语义检索**：使用先进的向量检索技术，准确找到相关内容
4. **多轮对话**：支持上下文相关的连续对话
5. **文档浏览**：可以浏览和搜索已上传的文档

您可以先上传一些文档，然后就可以开始提问了。有什么具体问题我可以帮您解答吗？',
    '{"model": "gpt-3.5-turbo", "tokens": 150, "timestamp": "2024-01-15T10:00:05Z"}'
);

-- 更新对话的最后消息时间
UPDATE conversations 
SET last_message_at = CURRENT_TIMESTAMP 
WHERE title = '欢迎对话';

-- 插入示例任务
INSERT INTO job_queue (job_type, job_data, status, priority) VALUES
('document_processing', '{"document_id": "sample-doc-id", "action": "parse"}', 'completed', 1),
('vector_generation', '{"chunk_ids": ["chunk-1", "chunk-2"], "model": "text-embedding-ada-002"}', 'completed', 2),
('index_update', '{"collection": "documents", "operation": "rebuild"}', 'pending', 3);

-- 插入审计日志示例
INSERT INTO audit_logs (user_id, action, resource_type, resource_id, new_values, ip_address, user_agent)
SELECT 
    u.id,
    'user_login',
    'user',
    u.id::text,
    jsonb_build_object('login_time', CURRENT_TIMESTAMP),
    '127.0.0.1'::inet,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
FROM users u WHERE u.email = '<EMAIL>';

-- 创建用户偏好设置
INSERT INTO user_preferences (user_id, language, timezone, theme, notifications)
SELECT 
    u.id,
    'zh-CN',
    'Asia/Shanghai',
    'light',
    jsonb_build_object(
        'email_notifications', true,
        'push_notifications', false,
        'document_processing', true,
        'system_updates', true
    )
FROM users u;

-- 创建示例文档块（模拟已处理的文档）
WITH demo_doc AS (
    SELECT d.id FROM documents d 
    JOIN users u ON d.user_id = u.id 
    WHERE u.email = '<EMAIL>' AND d.filename = 'sample-document.pdf'
)
INSERT INTO document_chunks (document_id, chunk_index, content, content_hash, start_position, end_position, token_count, metadata, vectorized, vector_id) VALUES
(
    (SELECT id FROM demo_doc),
    0,
    'RAG（检索增强生成）是一种结合了信息检索和文本生成的AI技术。它通过检索相关文档片段，然后基于这些片段生成准确的回答。',
    'hash001',
    0,
    100,
    50,
    jsonb_build_object('page', 1, 'section', '介绍'),
    true,
    'vector_001'
),
(
    (SELECT id FROM demo_doc),
    1,
    '向量数据库是RAG系统的核心组件之一，它能够高效地存储和检索文档的向量表示。常用的向量数据库包括Pinecone、Weaviate和Chroma等。',
    'hash002',
    101,
    200,
    45,
    jsonb_build_object('page', 1, 'section', '技术架构'),
    true,
    'vector_002'
),
(
    (SELECT id FROM demo_doc),
    2,
    '大语言模型（LLM）如GPT-3.5、GPT-4等在RAG系统中负责理解用户问题并生成自然语言回答。通过结合检索到的相关信息，LLM能够提供更准确和有用的回答。',
    'hash003',
    201,
    300,
    55,
    jsonb_build_object('page', 2, 'section', 'LLM集成'),
    true,
    'vector_003'
);

-- 提交事务
COMMIT;
