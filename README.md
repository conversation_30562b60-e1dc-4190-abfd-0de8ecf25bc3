# RAG智能问答系统

一个完整的RAG（检索增强生成）服务系统，集成大语言模型接口，支持文档上传、语义检索和智能问答。

## 🚀 项目特性

- **📄 多格式文档支持**：PDF、Word、TXT、Markdown等格式
- **🔍 智能语义检索**：基于向量相似度的精准检索
- **🤖 多LLM集成**：支持OpenAI GPT、Claude等主流模型
- **💬 多轮对话**：上下文相关的连续对话体验
- **🎨 现代化界面**：React + Next.js响应式前端
- **🏗️ 微服务架构**：可扩展的分布式系统设计
- **🔒 企业级安全**：JWT认证、RBAC权限控制
- **📊 实时监控**：完善的系统监控和日志记录

## 🏛️ 系统架构

```
┌─────────────────────────────────────────────────────────┐
│                    前端应用层                            │
│              Web前端 + 管理后台                          │
├─────────────────────────────────────────────────────────┤
│                    API网关层                             │
│            路由、认证、限流、监控                        │
├─────────────────────────────────────────────────────────┤
│                   业务服务层                             │
│    用户服务 | 文档服务 | 向量服务 | 检索服务 | 生成服务    │
├─────────────────────────────────────────────────────────┤
│                   数据存储层                             │
│         PostgreSQL | 向量数据库 | Redis | 对象存储       │
└─────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 后端技术
- **Node.js + TypeScript**：业务逻辑服务
- **Python + FastAPI**：AI相关服务
- **PostgreSQL**：关系数据存储
- **Redis**：缓存和会话管理
- **向量数据库**：Pinecone/Weaviate/Chroma
- **MinIO**：对象存储服务

### 前端技术
- **React 18**：用户界面框架
- **Next.js 14**：全栈React框架
- **TypeScript**：类型安全开发
- **Tailwind CSS**：原子化CSS框架

### 基础设施
- **Docker**：容器化部署
- **Kubernetes**：容器编排（可选）
- **Prometheus + Grafana**：监控告警
- **ELK Stack**：日志收集分析

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- Docker & Docker Compose
- PostgreSQL 13+
- Redis 6+

### 1. 克隆项目
```bash
git clone https://github.com/your-org/ai-rag-system.git
cd ai-rag-system
```

### 2. 环境配置
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量，配置API密钥等
vim .env
```

### 3. 启动服务
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 或者分别启动各个服务
npm run dev
```

### 4. 数据库初始化
```bash
# 运行数据库迁移
npm run db:migrate

# 插入种子数据
npm run db:seed
```

### 5. 访问应用
- **前端应用**：http://localhost:3100
- **API网关**：http://localhost:3000
- **管理后台**：http://localhost:3100/admin
- **API文档**：http://localhost:3000/docs

