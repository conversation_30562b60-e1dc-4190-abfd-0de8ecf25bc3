# 数据库配置
DATABASE_URL=postgresql://rag_user:rag_password@localhost:5432/rag_system
POSTGRES_DB=rag_system
POSTGRES_USER=rag_user
POSTGRES_PASSWORD=rag_password

# Redis 配置
REDIS_URL=redis://localhost:6379

# MinIO 对象存储配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=rag-documents

# 向量数据库配置
CHROMA_URL=http://localhost:8000
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
WEAVIATE_URL=http://localhost:8080

# LLM API 配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORG_ID=your-openai-org-id
ANTHROPIC_API_KEY=your-anthropic-api-key

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 应用配置
NODE_ENV=development
PORT=3000
API_VERSION=v1

# 服务URL配置
USER_SERVICE_URL=http://localhost:3001
DOCUMENT_SERVICE_URL=http://localhost:3002
EMBEDDING_SERVICE_URL=http://localhost:8001
RETRIEVAL_SERVICE_URL=http://localhost:8002
GENERATION_SERVICE_URL=http://localhost:3003
CONVERSATION_SERVICE_URL=http://localhost:3004

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:3000

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
FROM_EMAIL=<EMAIL>

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3200

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# 安全配置
CORS_ORIGIN=http://localhost:3100
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 文件上传配置
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,docx,txt,md

# 缓存配置
CACHE_TTL_SECONDS=3600
VECTOR_CACHE_TTL_SECONDS=86400
SEARCH_CACHE_TTL_SECONDS=1800

# 向量化配置
EMBEDDING_MODEL=text-embedding-ada-002
EMBEDDING_DIMENSION=1536
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# 检索配置
DEFAULT_TOP_K=10
DEFAULT_SIMILARITY_THRESHOLD=0.7
MAX_TOP_K=50

# 生成配置
DEFAULT_MODEL=gpt-3.5-turbo
DEFAULT_TEMPERATURE=0.7
DEFAULT_MAX_TOKENS=2000
STREAM_RESPONSE=true
