# RAG系统API接口设计

## 1. API设计原则

### 1.1 设计原则
- **RESTful风格**：遵循REST架构原则，使用标准HTTP方法
- **统一响应格式**：所有API返回统一的响应结构
- **版本控制**：支持API版本管理，向后兼容
- **安全认证**：JWT令牌认证，RBAC权限控制
- **错误处理**：标准化错误码和错误信息
- **文档完善**：自动生成API文档，提供示例代码

### 1.2 基础URL结构
```
https://api.rag-system.com/v1/{resource}
```

### 1.3 统一响应格式
```typescript
// 成功响应
interface SuccessResponse<T> {
  success: true;
  data: T;
  message?: string;
  timestamp: string;
  requestId: string;
}

// 错误响应
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  requestId: string;
}

// 分页响应
interface PaginatedResponse<T> {
  success: true;
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  timestamp: string;
  requestId: string;
}
```

## 2. 认证授权API

### 2.1 用户认证
```typescript
// POST /v1/auth/login
interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface LoginResponse {
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
    permissions: string[];
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
}

// POST /v1/auth/refresh
interface RefreshTokenRequest {
  refreshToken: string;
}

interface RefreshTokenResponse {
  accessToken: string;
  expiresIn: number;
}

// POST /v1/auth/logout
interface LogoutRequest {
  refreshToken: string;
}
```

### 2.2 用户管理
```typescript
// GET /v1/users/profile
interface UserProfile {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: string;
  permissions: string[];
  createdAt: string;
  lastLoginAt: string;
  preferences: {
    language: string;
    timezone: string;
    theme: 'light' | 'dark';
  };
}

// PUT /v1/users/profile
interface UpdateProfileRequest {
  name?: string;
  avatar?: string;
  preferences?: {
    language?: string;
    timezone?: string;
    theme?: 'light' | 'dark';
  };
}

// POST /v1/users/change-password
interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}
```

## 3. 文档管理API

### 3.1 文档上传
```typescript
// POST /v1/documents/upload
// Content-Type: multipart/form-data
interface UploadDocumentRequest {
  file: File;                    // 文档文件
  title?: string;                // 文档标题
  description?: string;          // 文档描述
  tags?: string[];               // 文档标签
  category?: string;             // 文档分类
}

interface UploadDocumentResponse {
  document: {
    id: string;
    filename: string;
    title: string;
    fileType: string;
    fileSize: number;
    status: 'uploading' | 'processing' | 'completed' | 'failed';
    uploadedAt: string;
    processingProgress?: number;
  };
}

// POST /v1/documents/upload/url
interface UploadByUrlRequest {
  url: string;
  title?: string;
  description?: string;
  tags?: string[];
}
```

### 3.2 文档管理
```typescript
// GET /v1/documents
interface ListDocumentsQuery {
  page?: number;
  pageSize?: number;
  status?: 'uploading' | 'processing' | 'completed' | 'failed';
  category?: string;
  tags?: string[];
  search?: string;
  sortBy?: 'createdAt' | 'title' | 'fileSize';
  sortOrder?: 'asc' | 'desc';
}

interface DocumentListItem {
  id: string;
  filename: string;
  title: string;
  description?: string;
  fileType: string;
  fileSize: number;
  status: string;
  tags: string[];
  category?: string;
  uploadedAt: string;
  processedAt?: string;
  chunksCount?: number;
}

// GET /v1/documents/{id}
interface DocumentDetail {
  id: string;
  filename: string;
  title: string;
  description?: string;
  fileType: string;
  fileSize: number;
  status: string;
  tags: string[];
  category?: string;
  uploadedAt: string;
  processedAt?: string;
  processingLog?: string[];
  chunks: {
    total: number;
    processed: number;
    failed: number;
  };
  metadata: {
    author?: string;
    createdDate?: string;
    modifiedDate?: string;
    pageCount?: number;
    wordCount?: number;
  };
}

// PUT /v1/documents/{id}
interface UpdateDocumentRequest {
  title?: string;
  description?: string;
  tags?: string[];
  category?: string;
}

// DELETE /v1/documents/{id}
// 删除文档及其相关的向量数据

// POST /v1/documents/{id}/reprocess
// 重新处理文档
```

### 3.3 文档内容
```typescript
// GET /v1/documents/{id}/content
interface DocumentContent {
  id: string;
  content: string;
  chunks: Array<{
    id: string;
    content: string;
    startPosition: number;
    endPosition: number;
    metadata: {
      pageNumber?: number;
      section?: string;
    };
  }>;
}

// GET /v1/documents/{id}/chunks
interface ListChunksQuery {
  page?: number;
  pageSize?: number;
  search?: string;
}

interface ChunkListItem {
  id: string;
  content: string;
  startPosition: number;
  endPosition: number;
  chunkIndex: number;
  vectorized: boolean;
  metadata: {
    pageNumber?: number;
    section?: string;
  };
}
```

## 4. 检索和问答API

### 4.1 语义检索
```typescript
// POST /v1/search
interface SearchRequest {
  query: string;                 // 查询问题
  topK?: number;                 // 返回结果数量，默认10
  similarityThreshold?: number;  // 相似度阈值，默认0.7
  filters?: {
    documentIds?: string[];      // 指定文档范围
    tags?: string[];             // 标签过滤
    categories?: string[];       // 分类过滤
    dateRange?: {
      start: string;
      end: string;
    };
  };
  retrievalStrategy?: 'semantic' | 'hybrid' | 'keyword';
  includeMetadata?: boolean;     // 是否包含元数据
}

interface SearchResponse {
  query: string;
  results: Array<{
    id: string;
    chunkId: string;
    documentId: string;
    content: string;
    score: number;
    metadata: {
      documentTitle: string;
      documentType: string;
      pageNumber?: number;
      section?: string;
      tags: string[];
    };
    highlights?: string[];       // 高亮片段
  }>;
  totalResults: number;
  processingTime: number;       // 处理时间(毫秒)
}
```

### 4.2 问答生成
```typescript
// POST /v1/chat/completions
interface ChatCompletionRequest {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  model?: string;               // 使用的模型
  temperature?: number;         // 生成温度，默认0.7
  maxTokens?: number;          // 最大token数
  stream?: boolean;            // 是否流式返回
  retrievalConfig?: {
    enabled: boolean;
    topK?: number;
    filters?: SearchRequest['filters'];
  };
  promptTemplate?: string;     // 自定义提示词模板
}

interface ChatCompletionResponse {
  id: string;
  object: 'chat.completion';
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: 'assistant';
      content: string;
    };
    finishReason: 'stop' | 'length' | 'content_filter';
  }>;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  retrievalResults?: SearchResponse['results'];
}

// 流式响应格式
interface ChatCompletionChunk {
  id: string;
  object: 'chat.completion.chunk';
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: {
      role?: 'assistant';
      content?: string;
    };
    finishReason?: 'stop' | 'length' | 'content_filter';
  }>;
}
```

### 4.3 对话管理
```typescript
// POST /v1/conversations
interface CreateConversationRequest {
  title?: string;
  description?: string;
  systemPrompt?: string;
}

interface ConversationResponse {
  id: string;
  title: string;
  description?: string;
  systemPrompt?: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
}

// GET /v1/conversations
interface ListConversationsQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'title';
  sortOrder?: 'asc' | 'desc';
}

// GET /v1/conversations/{id}/messages
interface ListMessagesQuery {
  page?: number;
  pageSize?: number;
  before?: string;              // 消息ID，获取之前的消息
  after?: string;               // 消息ID，获取之后的消息
}

interface MessageResponse {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  createdAt: string;
  metadata?: {
    model?: string;
    tokens?: number;
    retrievalResults?: SearchResponse['results'];
  };
}

// DELETE /v1/conversations/{id}
// 删除对话及其所有消息
```

## 5. 系统管理API

### 5.1 系统配置
```typescript
// GET /v1/admin/config
interface SystemConfig {
  embedding: {
    model: string;
    dimension: number;
    batchSize: number;
  };
  retrieval: {
    defaultTopK: number;
    defaultThreshold: number;
    maxTopK: number;
  };
  generation: {
    defaultModel: string;
    defaultTemperature: number;
    maxTokens: number;
    availableModels: string[];
  };
  storage: {
    maxFileSize: number;
    allowedFileTypes: string[];
    retentionDays: number;
  };
}

// PUT /v1/admin/config
interface UpdateConfigRequest {
  embedding?: Partial<SystemConfig['embedding']>;
  retrieval?: Partial<SystemConfig['retrieval']>;
  generation?: Partial<SystemConfig['generation']>;
  storage?: Partial<SystemConfig['storage']>;
}
```

### 5.2 系统监控
```typescript
// GET /v1/admin/stats
interface SystemStats {
  documents: {
    total: number;
    processing: number;
    completed: number;
    failed: number;
  };
  vectors: {
    total: number;
    dimension: number;
    indexSize: string;
  };
  conversations: {
    total: number;
    activeToday: number;
    messagesTotal: number;
  };
  usage: {
    apiCalls: {
      today: number;
      thisMonth: number;
    };
    tokens: {
      today: number;
      thisMonth: number;
    };
    storage: {
      used: string;
      total: string;
      percentage: number;
    };
  };
}

// GET /v1/admin/health
interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    database: {
      status: 'up' | 'down';
      responseTime: number;
    };
    vectorDatabase: {
      status: 'up' | 'down';
      responseTime: number;
    };
    cache: {
      status: 'up' | 'down';
      responseTime: number;
    };
    messageQueue: {
      status: 'up' | 'down';
      queueSize: number;
    };
    externalAPIs: {
      embedding: {
        status: 'up' | 'down';
        responseTime: number;
      };
      llm: {
        status: 'up' | 'down';
        responseTime: number;
      };
    };
  };
}
```

## 6. 错误处理

### 6.1 标准错误码
```typescript
enum ErrorCode {
  // 通用错误 (1000-1999)
  INTERNAL_SERVER_ERROR = 'E1000',
  INVALID_REQUEST = 'E1001',
  UNAUTHORIZED = 'E1002',
  FORBIDDEN = 'E1003',
  NOT_FOUND = 'E1004',
  RATE_LIMIT_EXCEEDED = 'E1005',
  
  // 认证错误 (2000-2099)
  INVALID_CREDENTIALS = 'E2000',
  TOKEN_EXPIRED = 'E2001',
  TOKEN_INVALID = 'E2002',
  ACCOUNT_LOCKED = 'E2003',
  
  // 文档错误 (3000-3099)
  FILE_TOO_LARGE = 'E3000',
  UNSUPPORTED_FILE_TYPE = 'E3001',
  DOCUMENT_NOT_FOUND = 'E3002',
  DOCUMENT_PROCESSING_FAILED = 'E3003',
  DOCUMENT_ALREADY_EXISTS = 'E3004',
  
  // 检索错误 (4000-4099)
  QUERY_TOO_SHORT = 'E4000',
  QUERY_TOO_LONG = 'E4001',
  NO_RESULTS_FOUND = 'E4002',
  RETRIEVAL_SERVICE_UNAVAILABLE = 'E4003',
  
  // 生成错误 (5000-5099)
  GENERATION_FAILED = 'E5000',
  MODEL_UNAVAILABLE = 'E5001',
  CONTEXT_TOO_LONG = 'E5002',
  CONTENT_FILTERED = 'E5003',
}
```

### 6.2 错误响应示例
```typescript
// 400 Bad Request
{
  "success": false,
  "error": {
    "code": "E1001",
    "message": "请求参数无效",
    "details": {
      "field": "query",
      "reason": "查询内容不能为空"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}

// 401 Unauthorized
{
  "success": false,
  "error": {
    "code": "E2001",
    "message": "访问令牌已过期",
    "details": {
      "expiredAt": "2024-01-15T10:00:00Z"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}

// 429 Too Many Requests
{
  "success": false,
  "error": {
    "code": "E1005",
    "message": "请求频率超限",
    "details": {
      "limit": 100,
      "window": "1h",
      "retryAfter": 3600
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

## 7. API安全

### 7.1 认证方式
```typescript
// JWT Bearer Token
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// API Key (用于服务间调用)
X-API-Key: sk-1234567890abcdef...

// 请求签名 (高安全场景)
X-Signature: sha256=abc123...
X-Timestamp: 1642234567
```

### 7.2 限流策略
```typescript
interface RateLimitConfig {
  // 用户级别限流
  userLimits: {
    requests: {
      perMinute: 60;
      perHour: 1000;
      perDay: 10000;
    };
    uploads: {
      perHour: 10;
      perDay: 100;
    };
    tokens: {
      perDay: 100000;
    };
  };
  
  // IP级别限流
  ipLimits: {
    requests: {
      perMinute: 100;
      perHour: 2000;
    };
  };
  
  // API Key级别限流
  apiKeyLimits: {
    requests: {
      perMinute: 1000;
      perHour: 50000;
    };
  };
}
```

## 8. API版本控制

### 8.1 版本策略
- **URL版本控制**：`/v1/`, `/v2/`
- **向后兼容**：旧版本API继续支持
- **废弃通知**：提前通知API废弃计划
- **迁移指南**：提供版本迁移文档

### 8.2 版本响应头
```typescript
// 响应头
X-API-Version: v1
X-API-Deprecated: false
X-API-Sunset: 2025-01-01T00:00:00Z  // 废弃时间
```
