# RAG系统数据库设计

## 1. 数据库架构概述

### 1.1 数据库选型
- **关系数据库**：PostgreSQL 13+ (主要业务数据)
- **向量数据库**：Pinecone/Weaviate/Chroma (向量存储和检索)
- **缓存数据库**：Redis 6+ (缓存和会话)
- **对象存储**：AWS S3/MinIO (文件存储)

### 1.2 数据分层策略
```
┌─────────────────────────────────────────────────────────┐
│                    应用层                                │
├─────────────────────────────────────────────────────────┤
│                   缓存层                                 │
│              Redis (热数据缓存)                          │
├─────────────────────────────────────────────────────────┤
│                  业务数据层                              │
│         PostgreSQL (结构化数据存储)                      │
├─────────────────────────────────────────────────────────┤
│                  向量数据层                              │
│        向量数据库 (向量存储和相似度检索)                  │
├─────────────────────────────────────────────────────────┤
│                  文件存储层                              │
│           对象存储 (原始文档存储)                        │
└─────────────────────────────────────────────────────────┘
```

## 2. PostgreSQL数据库设计

### 2.1 用户管理模块

#### 用户表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT users_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_role_check CHECK (role IN ('admin', 'user', 'viewer')),
    CONSTRAINT users_status_check CHECK (status IN ('active', 'inactive', 'suspended'))
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### 用户配置表 (user_preferences)
```sql
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    language VARCHAR(10) DEFAULT 'zh-CN',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    theme VARCHAR(20) DEFAULT 'light',
    notifications JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT user_preferences_theme_check CHECK (theme IN ('light', 'dark', 'auto')),
    UNIQUE(user_id)
);

-- 索引
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);
```

#### 用户会话表 (user_sessions)
```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    refresh_token_hash VARCHAR(255) NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(refresh_token_hash)
);

-- 索引
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_refresh_token ON user_sessions(refresh_token_hash);
```

### 2.2 文档管理模块

#### 文档表 (documents)
```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    file_type VARCHAR(50) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256哈希
    storage_path TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'uploading',
    processing_progress INTEGER DEFAULT 0,
    processing_log JSONB DEFAULT '[]',
    tags TEXT[] DEFAULT '{}',
    category VARCHAR(100),
    metadata JSONB DEFAULT '{}',
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT documents_status_check CHECK (status IN ('uploading', 'processing', 'completed', 'failed')),
    CONSTRAINT documents_progress_check CHECK (processing_progress >= 0 AND processing_progress <= 100),
    CONSTRAINT documents_file_size_check CHECK (file_size > 0)
);

-- 索引
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_file_type ON documents(file_type);
CREATE INDEX idx_documents_tags ON documents USING GIN(tags);
CREATE INDEX idx_documents_category ON documents(category);
CREATE INDEX idx_documents_created_at ON documents(created_at);
CREATE INDEX idx_documents_file_hash ON documents(file_hash);
CREATE INDEX idx_documents_title_search ON documents USING GIN(to_tsvector('english', title));
```

#### 文档块表 (document_chunks)
```sql
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64) NOT NULL, -- 内容哈希，用于去重
    start_position INTEGER NOT NULL,
    end_position INTEGER NOT NULL,
    token_count INTEGER,
    metadata JSONB DEFAULT '{}', -- 页码、章节等信息
    vectorized BOOLEAN DEFAULT FALSE,
    vector_id VARCHAR(255), -- 向量数据库中的ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT document_chunks_positions_check CHECK (start_position >= 0 AND end_position > start_position),
    CONSTRAINT document_chunks_token_count_check CHECK (token_count IS NULL OR token_count > 0),
    UNIQUE(document_id, chunk_index)
);

-- 索引
CREATE INDEX idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_document_chunks_vectorized ON document_chunks(vectorized);
CREATE INDEX idx_document_chunks_vector_id ON document_chunks(vector_id);
CREATE INDEX idx_document_chunks_content_hash ON document_chunks(content_hash);
CREATE INDEX idx_document_chunks_content_search ON document_chunks USING GIN(to_tsvector('english', content));
```

### 2.3 对话管理模块

#### 对话表 (conversations)
```sql
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    system_prompt TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    message_count INTEGER DEFAULT 0,
    last_message_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT conversations_status_check CHECK (status IN ('active', 'archived', 'deleted')),
    CONSTRAINT conversations_message_count_check CHECK (message_count >= 0)
);

-- 索引
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_last_message_at ON conversations(last_message_at);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);
CREATE INDEX idx_conversations_title_search ON conversations USING GIN(to_tsvector('english', title));
```

#### 消息表 (messages)
```sql
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}', -- 模型信息、token使用量等
    parent_message_id UUID REFERENCES messages(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT messages_role_check CHECK (role IN ('user', 'assistant', 'system'))
);

-- 索引
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_role ON messages(role);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_messages_parent_message_id ON messages(parent_message_id);
```

#### 检索结果表 (retrieval_results)
```sql
CREATE TABLE retrieval_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    chunk_id UUID NOT NULL REFERENCES document_chunks(id) ON DELETE CASCADE,
    score DECIMAL(5,4) NOT NULL,
    rank_position INTEGER NOT NULL,
    retrieval_strategy VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT retrieval_results_score_check CHECK (score >= 0 AND score <= 1),
    CONSTRAINT retrieval_results_rank_check CHECK (rank_position > 0),
    UNIQUE(message_id, chunk_id)
);

-- 索引
CREATE INDEX idx_retrieval_results_message_id ON retrieval_results(message_id);
CREATE INDEX idx_retrieval_results_chunk_id ON retrieval_results(chunk_id);
CREATE INDEX idx_retrieval_results_score ON retrieval_results(score DESC);

### 2.4 系统管理模块

#### 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    config_type VARCHAR(50) NOT NULL,
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT system_configs_type_check CHECK (config_type IN ('embedding', 'retrieval', 'generation', 'storage', 'security'))
);

-- 索引
CREATE INDEX idx_system_configs_key ON system_configs(config_key);
CREATE INDEX idx_system_configs_type ON system_configs(config_type);
```

#### 任务队列表 (job_queue)
```sql
CREATE TABLE job_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_type VARCHAR(50) NOT NULL,
    job_data JSONB NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    priority INTEGER DEFAULT 0,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    scheduled_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT job_queue_status_check CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    CONSTRAINT job_queue_attempts_check CHECK (attempts >= 0 AND attempts <= max_attempts),
    CONSTRAINT job_queue_priority_check CHECK (priority >= 0)
);

-- 索引
CREATE INDEX idx_job_queue_status ON job_queue(status);
CREATE INDEX idx_job_queue_type ON job_queue(job_type);
CREATE INDEX idx_job_queue_scheduled_at ON job_queue(scheduled_at);
CREATE INDEX idx_job_queue_priority ON job_queue(priority DESC);
```

#### 审计日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource_type ON audit_logs(resource_type);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

## 3. 向量数据库设计

### 3.1 Pinecone配置
```typescript
// Pinecone索引配置
interface PineconeIndexConfig {
  name: string;                    // 索引名称
  dimension: number;               // 向量维度 (如1536 for OpenAI)
  metric: 'cosine' | 'euclidean' | 'dotproduct';
  pods: number;                    // Pod数量
  replicas: number;                // 副本数量
  metadata_config: {
    indexed: string[];             // 需要索引的元数据字段
  };
}

// 向量记录结构
interface VectorRecord {
  id: string;                      // 向量ID (对应chunk_id)
  values: number[];                // 向量值
  metadata: {
    document_id: string;           // 文档ID
    chunk_index: number;           // 块索引
    content: string;               // 文本内容
    title: string;                 // 文档标题
    file_type: string;             // 文件类型
    tags: string[];                // 标签
    created_at: string;            // 创建时间
    token_count: number;           // Token数量
  };
}
```

### 3.2 Weaviate配置
```typescript
// Weaviate类定义
interface WeaviateClass {
  class: string;                   // 类名 (如DocumentChunk)
  description: string;             // 类描述
  vectorizer: string;              // 向量化器
  moduleConfig: {
    'text2vec-openai': {
      model: string;               // 模型名称
      modelVersion: string;        // 模型版本
      type: string;                // 类型
    };
  };
  properties: Array<{
    name: string;                  // 属性名
    dataType: string[];            // 数据类型
    description: string;           // 属性描述
    tokenization?: string;         // 分词方式
    indexFilterable?: boolean;     // 是否可过滤
    indexSearchable?: boolean;     // 是否可搜索
  }>;
}

// DocumentChunk类定义示例
const DocumentChunkClass: WeaviateClass = {
  class: 'DocumentChunk',
  description: '文档块，包含文档的分块内容和元数据',
  vectorizer: 'text2vec-openai',
  moduleConfig: {
    'text2vec-openai': {
      model: 'text-embedding-ada-002',
      modelVersion: '002',
      type: 'text'
    }
  },
  properties: [
    {
      name: 'content',
      dataType: ['text'],
      description: '文档块的文本内容',
      tokenization: 'word'
    },
    {
      name: 'documentId',
      dataType: ['string'],
      description: '所属文档的ID',
      indexFilterable: true
    },
    {
      name: 'chunkIndex',
      dataType: ['int'],
      description: '块在文档中的索引',
      indexFilterable: true
    },
    {
      name: 'title',
      dataType: ['string'],
      description: '文档标题',
      indexSearchable: true
    },
    {
      name: 'fileType',
      dataType: ['string'],
      description: '文件类型',
      indexFilterable: true
    },
    {
      name: 'tags',
      dataType: ['string[]'],
      description: '文档标签',
      indexFilterable: true
    },
    {
      name: 'createdAt',
      dataType: ['date'],
      description: '创建时间',
      indexFilterable: true
    }
  ]
};
```

### 3.3 Chroma配置
```typescript
// Chroma集合配置
interface ChromaCollectionConfig {
  name: string;                    // 集合名称
  metadata: {
    description: string;           // 集合描述
    embedding_function: string;    // 嵌入函数
  };
  embedding_function: {
    name: string;                  // 函数名称
    model: string;                 // 模型名称
    api_key?: string;              // API密钥
  };
}

// 文档记录结构
interface ChromaDocument {
  ids: string[];                   // 文档ID列表
  embeddings?: number[][];         // 嵌入向量 (可选，自动生成)
  documents: string[];             // 文档内容
  metadatas: Array<{
    document_id: string;
    chunk_index: number;
    title: string;
    file_type: string;
    tags: string[];
    created_at: string;
  }>;
}
```

## 4. Redis缓存设计

### 4.1 缓存键命名规范
```typescript
// 缓存键命名空间
enum CacheNamespace {
  USER_SESSION = 'session',       // 用户会话
  DOCUMENT_CACHE = 'doc',         // 文档缓存
  VECTOR_CACHE = 'vector',        // 向量缓存
  SEARCH_CACHE = 'search',        // 搜索缓存
  GENERATION_CACHE = 'gen',       // 生成缓存
  RATE_LIMIT = 'rate',            // 限流
  CONFIG_CACHE = 'config'         // 配置缓存
}

// 缓存键生成函数
class CacheKeyGenerator {
  static userSession(userId: string): string {
    return `${CacheNamespace.USER_SESSION}:${userId}`;
  }

  static documentContent(documentId: string): string {
    return `${CacheNamespace.DOCUMENT_CACHE}:content:${documentId}`;
  }

  static vectorEmbedding(contentHash: string): string {
    return `${CacheNamespace.VECTOR_CACHE}:${contentHash}`;
  }

  static searchResult(queryHash: string): string {
    return `${CacheNamespace.SEARCH_CACHE}:${queryHash}`;
  }

  static generationResult(promptHash: string): string {
    return `${CacheNamespace.GENERATION_CACHE}:${promptHash}`;
  }

  static rateLimitUser(userId: string, window: string): string {
    return `${CacheNamespace.RATE_LIMIT}:user:${userId}:${window}`;
  }

  static rateLimitIP(ip: string, window: string): string {
    return `${CacheNamespace.RATE_LIMIT}:ip:${ip}:${window}`;
  }
}
```

### 4.2 缓存策略配置
```typescript
// 缓存配置
interface CacheConfig {
  userSession: {
    ttl: number;                   // 7天
    maxSize: string;               // 最大大小
  };
  documentContent: {
    ttl: number;                   // 1小时
    maxSize: string;
  };
  vectorEmbedding: {
    ttl: number;                   // 24小时
    maxSize: string;
  };
  searchResult: {
    ttl: number;                   // 30分钟
    maxSize: string;
  };
  generationResult: {
    ttl: number;                   // 1小时
    maxSize: string;
  };
}

const cacheConfig: CacheConfig = {
  userSession: {
    ttl: 7 * 24 * 60 * 60,        // 7天
    maxSize: '1MB'
  },
  documentContent: {
    ttl: 60 * 60,                 // 1小时
    maxSize: '10MB'
  },
  vectorEmbedding: {
    ttl: 24 * 60 * 60,            // 24小时
    maxSize: '100KB'
  },
  searchResult: {
    ttl: 30 * 60,                 // 30分钟
    maxSize: '1MB'
  },
  generationResult: {
    ttl: 60 * 60,                 // 1小时
    maxSize: '100KB'
  }
};
```

## 5. 数据库关系图

### 5.1 ER图设计
```mermaid
erDiagram
    users ||--o{ documents : owns
    users ||--o{ conversations : creates
    users ||--o{ user_preferences : has
    users ||--o{ user_sessions : has
    users ||--o{ audit_logs : generates

    documents ||--o{ document_chunks : contains
    document_chunks ||--o{ retrieval_results : referenced_in

    conversations ||--o{ messages : contains
    messages ||--o{ retrieval_results : uses
    messages ||--o{ messages : replies_to

    users {
        uuid id PK
        varchar email UK
        varchar password_hash
        varchar name
        varchar role
        varchar status
        timestamp created_at
    }

    documents {
        uuid id PK
        uuid user_id FK
        varchar filename
        varchar title
        varchar file_type
        bigint file_size
        varchar status
        timestamp created_at
    }

    document_chunks {
        uuid id PK
        uuid document_id FK
        integer chunk_index
        text content
        varchar content_hash
        boolean vectorized
        varchar vector_id
    }

    conversations {
        uuid id PK
        uuid user_id FK
        varchar title
        varchar status
        integer message_count
        timestamp created_at
    }

    messages {
        uuid id PK
        uuid conversation_id FK
        uuid parent_message_id FK
        varchar role
        text content
        jsonb metadata
        timestamp created_at
    }

    retrieval_results {
        uuid id PK
        uuid message_id FK
        uuid chunk_id FK
        decimal score
        integer rank_position
        varchar retrieval_strategy
    }
```

## 6. 数据迁移和版本控制

### 6.1 数据库迁移脚本
```sql
-- 创建迁移版本表
CREATE TABLE schema_migrations (
    version VARCHAR(255) PRIMARY KEY,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 初始化版本
INSERT INTO schema_migrations (version) VALUES ('001_initial_schema');
```

### 6.2 迁移脚本示例
```sql
-- migrations/002_add_document_categories.sql
BEGIN;

-- 添加文档分类表
CREATE TABLE document_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7), -- 十六进制颜色代码
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认分类
INSERT INTO document_categories (name, description, color) VALUES
('技术文档', '技术相关的文档', '#2196F3'),
('业务文档', '业务流程和规范', '#4CAF50'),
('法律文档', '法律法规和合同', '#FF9800'),
('其他', '其他类型文档', '#9E9E9E');

-- 更新迁移版本
INSERT INTO schema_migrations (version) VALUES ('002_add_document_categories');

COMMIT;
```

## 7. 性能优化策略

### 7.1 索引优化
```sql
-- 复合索引优化
CREATE INDEX idx_documents_user_status_created ON documents(user_id, status, created_at DESC);
CREATE INDEX idx_messages_conversation_created ON messages(conversation_id, created_at DESC);
CREATE INDEX idx_chunks_document_vectorized ON document_chunks(document_id, vectorized);

-- 部分索引优化
CREATE INDEX idx_documents_processing ON documents(id) WHERE status IN ('processing', 'failed');
CREATE INDEX idx_chunks_unvectorized ON document_chunks(id) WHERE vectorized = FALSE;

-- 表达式索引
CREATE INDEX idx_documents_title_lower ON documents(LOWER(title));
CREATE INDEX idx_users_email_lower ON users(LOWER(email));
```

### 7.2 分区策略
```sql
-- 按时间分区的审计日志表
CREATE TABLE audit_logs_partitioned (
    LIKE audit_logs INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- 创建月度分区
CREATE TABLE audit_logs_2024_01 PARTITION OF audit_logs_partitioned
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE audit_logs_2024_02 PARTITION OF audit_logs_partitioned
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
```

### 7.3 查询优化
```sql
-- 使用物化视图优化统计查询
CREATE MATERIALIZED VIEW user_document_stats AS
SELECT
    u.id as user_id,
    u.name as user_name,
    COUNT(d.id) as document_count,
    SUM(d.file_size) as total_size,
    COUNT(CASE WHEN d.status = 'completed' THEN 1 END) as completed_count,
    MAX(d.created_at) as last_upload_at
FROM users u
LEFT JOIN documents d ON u.id = d.user_id
GROUP BY u.id, u.name;

-- 创建刷新索引
CREATE UNIQUE INDEX idx_user_document_stats_user_id ON user_document_stats(user_id);

-- 定时刷新物化视图
CREATE OR REPLACE FUNCTION refresh_user_document_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_document_stats;
END;
$$ LANGUAGE plpgsql;
```

## 8. 数据备份和恢复

### 8.1 备份策略
```bash
#!/bin/bash
# 数据库备份脚本

# 配置变量
DB_NAME="rag_system"
BACKUP_DIR="/backup/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 全量备份
pg_dump -h localhost -U postgres -d $DB_NAME \
    --format=custom \
    --compress=9 \
    --file=$BACKUP_DIR/rag_system_full_$DATE.dump

# 增量备份 (WAL归档)
pg_basebackup -h localhost -U postgres \
    --pgdata=$BACKUP_DIR/base_$DATE \
    --format=tar \
    --compress=9 \
    --progress \
    --verbose

# 清理旧备份 (保留30天)
find $BACKUP_DIR -name "*.dump" -mtime +30 -delete
find $BACKUP_DIR -name "base_*" -mtime +30 -exec rm -rf {} \;
```

### 8.2 恢复策略
```bash
#!/bin/bash
# 数据库恢复脚本

# 配置变量
DB_NAME="rag_system"
BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "使用方法: $0 <backup_file>"
    exit 1
fi

# 停止应用服务
systemctl stop rag-system

# 删除现有数据库
dropdb -h localhost -U postgres $DB_NAME

# 创建新数据库
createdb -h localhost -U postgres $DB_NAME

# 恢复数据
pg_restore -h localhost -U postgres -d $DB_NAME \
    --verbose \
    --clean \
    --if-exists \
    $BACKUP_FILE

# 重建索引和统计信息
psql -h localhost -U postgres -d $DB_NAME -c "REINDEX DATABASE $DB_NAME;"
psql -h localhost -U postgres -d $DB_NAME -c "ANALYZE;"

# 启动应用服务
systemctl start rag-system

echo "数据库恢复完成"
```

## 9. 监控和维护

### 9.1 数据库监控
```sql
-- 创建监控视图
CREATE VIEW db_performance_stats AS
SELECT
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation,
    most_common_vals,
    most_common_freqs
FROM pg_stats
WHERE schemaname = 'public';

-- 慢查询监控
CREATE VIEW slow_queries AS
SELECT
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
ORDER BY total_time DESC;
```

### 9.2 定期维护任务
```sql
-- 创建维护函数
CREATE OR REPLACE FUNCTION perform_maintenance()
RETURNS void AS $$
BEGIN
    -- 更新表统计信息
    ANALYZE;

    -- 清理过期会话
    DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP;

    -- 清理旧的审计日志 (保留1年)
    DELETE FROM audit_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 year';

    -- 清理失败的任务 (保留7天)
    DELETE FROM job_queue
    WHERE status = 'failed'
    AND created_at < CURRENT_TIMESTAMP - INTERVAL '7 days';

    -- 刷新物化视图
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_document_stats;

    RAISE NOTICE '维护任务完成';
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务 (需要pg_cron扩展)
SELECT cron.schedule('maintenance', '0 2 * * *', 'SELECT perform_maintenance();');
```
```
```
