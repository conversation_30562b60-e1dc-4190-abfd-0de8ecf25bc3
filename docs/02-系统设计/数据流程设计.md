# RAG系统数据流程设计

## 1. 数据流程概述

### 1.1 核心数据流
RAG系统的数据流程包含四个主要阶段：
1. **文档摄入流程**：文档上传、解析、预处理
2. **向量化流程**：文本分块、嵌入生成、向量存储
3. **检索流程**：查询理解、相似度计算、结果排序
4. **生成流程**：上下文构建、LLM调用、结果返回

### 1.2 数据流向图
```
文档上传 → 文档解析 → 文本分块 → 向量化 → 向量存储
                                              ↓
用户查询 → 查询向量化 → 相似度检索 ← ← ← ← ← ← ←
    ↓
上下文构建 → LLM生成 → 结果返回
```

## 2. 文档摄入流程

### 2.1 文档上传阶段
```mermaid
sequenceDiagram
    participant User as 用户
    participant Web as Web前端
    participant Gateway as API网关
    participant DocService as 文档服务
    participant Storage as 对象存储
    participant DB as PostgreSQL
    participant Queue as 消息队列

    User->>Web: 选择文档上传
    Web->>Gateway: POST /api/documents/upload
    Gateway->>DocService: 转发上传请求
    
    DocService->>DocService: 文档格式验证
    DocService->>DocService: 文件大小检查
    DocService->>Storage: 存储原始文档
    Storage-->>DocService: 返回存储路径
    
    DocService->>DB: 保存文档元数据
    DocService->>Queue: 发送处理任务
    DocService-->>Gateway: 返回上传结果
    Gateway-->>Web: 返回响应
    Web-->>User: 显示上传状态
```

#### 数据结构
```typescript
// 文档元数据
interface DocumentMetadata {
  id: string;                    // 文档唯一标识
  filename: string;              // 原始文件名
  fileType: string;              // 文件类型 (pdf, docx, txt等)
  fileSize: number;              // 文件大小(字节)
  uploadTime: Date;              // 上传时间
  userId: string;                // 上传用户ID
  status: 'uploading' | 'processing' | 'completed' | 'failed';
  storagePath: string;           // 存储路径
  tags: string[];                // 文档标签
  description?: string;          // 文档描述
}
```

### 2.2 文档解析阶段
```mermaid
sequenceDiagram
    participant Queue as 消息队列
    participant DocService as 文档服务
    participant Storage as 对象存储
    participant Parser as 文档解析器
    participant DB as PostgreSQL

    Queue->>DocService: 接收处理任务
    DocService->>Storage: 下载原始文档
    Storage-->>DocService: 返回文档内容
    
    DocService->>Parser: 调用对应解析器
    alt PDF文档
        Parser->>Parser: PDF文本提取
    else Word文档
        Parser->>Parser: DOCX文本提取
    else 纯文本
        Parser->>Parser: 文本编码处理
    end
    
    Parser-->>DocService: 返回解析结果
    DocService->>DocService: 文本清洗和预处理
    DocService->>DB: 保存解析后文本
    DocService->>Queue: 发送向量化任务
```

#### 文本预处理步骤
1. **编码规范化**：统一字符编码为UTF-8
2. **格式清理**：移除多余空白字符、特殊符号
3. **内容提取**：提取正文内容，过滤页眉页脚
4. **结构化处理**：识别标题、段落、列表等结构
5. **质量检查**：检查文本完整性和可读性

### 2.3 文本分块阶段
```typescript
// 分块策略配置
interface ChunkingConfig {
  strategy: 'fixed' | 'semantic' | 'sliding';  // 分块策略
  chunkSize: number;                           // 块大小(字符数)
  overlap: number;                             // 重叠大小
  separators: string[];                        // 分隔符
  preserveStructure: boolean;                  // 保持结构
}

// 文本块数据结构
interface TextChunk {
  id: string;                    // 块唯一标识
  documentId: string;            // 所属文档ID
  content: string;               // 块内容
  startPosition: number;         // 在原文档中的起始位置
  endPosition: number;           // 在原文档中的结束位置
  chunkIndex: number;            // 块序号
  metadata: {
    title?: string;              // 所属章节标题
    pageNumber?: number;         // 页码
    section?: string;            // 章节信息
  };
}
```

## 3. 向量化流程

### 3.1 向量化处理阶段
```mermaid
sequenceDiagram
    participant Queue as 消息队列
    participant EmbedService as 向量化服务
    participant DB as PostgreSQL
    participant EmbedModel as 嵌入模型
    participant VectorDB as 向量数据库
    participant Cache as Redis缓存

    Queue->>EmbedService: 接收向量化任务
    EmbedService->>DB: 获取文本块
    
    loop 批量处理文本块
        EmbedService->>Cache: 检查缓存
        alt 缓存命中
            Cache-->>EmbedService: 返回向量
        else 缓存未命中
            EmbedService->>EmbedModel: 调用嵌入模型
            EmbedModel-->>EmbedService: 返回向量
            EmbedService->>Cache: 缓存向量
        end
        
        EmbedService->>VectorDB: 存储向量
        EmbedService->>DB: 更新处理状态
    end
    
    EmbedService->>DB: 标记文档处理完成
```

#### 向量数据结构
```typescript
// 向量记录
interface VectorRecord {
  id: string;                    // 向量唯一标识
  chunkId: string;               // 对应文本块ID
  documentId: string;            // 所属文档ID
  vector: number[];              // 向量数据
  dimension: number;             // 向量维度
  model: string;                 // 使用的嵌入模型
  createdAt: Date;               // 创建时间
  metadata: {
    content: string;             // 原始文本内容
    title?: string;              // 文档标题
    tags: string[];              // 标签
  };
}
```

### 3.2 向量索引管理
```typescript
// 向量索引配置
interface VectorIndexConfig {
  indexType: 'flat' | 'ivf' | 'hnsw';  // 索引类型
  metric: 'cosine' | 'euclidean' | 'dot';  // 距离度量
  dimension: number;                    // 向量维度
  parameters: {
    nlist?: number;                     // IVF参数
    m?: number;                         // HNSW参数
    efConstruction?: number;            // HNSW构建参数
  };
}
```

## 4. 检索流程

### 4.1 查询处理阶段
```mermaid
sequenceDiagram
    participant User as 用户
    participant Web as Web前端
    participant Gateway as API网关
    participant RetrievalService as 检索服务
    participant EmbedService as 向量化服务
    participant VectorDB as 向量数据库
    participant DB as PostgreSQL
    participant Cache as Redis缓存

    User->>Web: 输入查询问题
    Web->>Gateway: POST /api/search
    Gateway->>RetrievalService: 转发查询请求
    
    RetrievalService->>RetrievalService: 查询预处理
    RetrievalService->>EmbedService: 查询向量化
    EmbedService-->>RetrievalService: 返回查询向量
    
    RetrievalService->>Cache: 检查缓存
    alt 缓存命中
        Cache-->>RetrievalService: 返回缓存结果
    else 缓存未命中
        RetrievalService->>VectorDB: 向量相似度检索
        VectorDB-->>RetrievalService: 返回相似向量
        
        RetrievalService->>DB: 获取文本内容
        DB-->>RetrievalService: 返回文本块
        
        RetrievalService->>RetrievalService: 结果重排序
        RetrievalService->>Cache: 缓存结果
    end
    
    RetrievalService-->>Gateway: 返回检索结果
    Gateway-->>Web: 返回响应
    Web-->>User: 显示检索结果
```

#### 检索配置
```typescript
// 检索参数
interface RetrievalConfig {
  topK: number;                         // 返回结果数量
  similarityThreshold: number;          // 相似度阈值
  retrievalStrategy: 'semantic' | 'hybrid' | 'keyword';
  rerankingModel?: string;              // 重排序模型
  filters: {
    documentIds?: string[];             // 文档ID过滤
    tags?: string[];                    // 标签过滤
    dateRange?: {                       // 时间范围过滤
      start: Date;
      end: Date;
    };
  };
}

// 检索结果
interface RetrievalResult {
  id: string;                           // 结果ID
  chunkId: string;                      // 文本块ID
  documentId: string;                   // 文档ID
  content: string;                      // 文本内容
  score: number;                        // 相似度分数
  metadata: {
    title: string;                      // 文档标题
    source: string;                     // 来源信息
    pageNumber?: number;                // 页码
  };
}
```

### 4.2 混合检索策略
```typescript
// 混合检索实现
class HybridRetrieval {
  async search(query: string, config: RetrievalConfig): Promise<RetrievalResult[]> {
    // 1. 语义检索
    const semanticResults = await this.semanticSearch(query, config);
    
    // 2. 关键词检索
    const keywordResults = await this.keywordSearch(query, config);
    
    // 3. 结果融合
    const fusedResults = this.fuseResults(semanticResults, keywordResults);
    
    // 4. 重排序
    const rerankedResults = await this.rerank(query, fusedResults);
    
    return rerankedResults.slice(0, config.topK);
  }
  
  private fuseResults(semantic: RetrievalResult[], keyword: RetrievalResult[]): RetrievalResult[] {
    // RRF (Reciprocal Rank Fusion) 算法
    const k = 60; // RRF参数
    const scoreMap = new Map<string, number>();
    
    // 计算语义检索分数
    semantic.forEach((result, index) => {
      const score = 1 / (k + index + 1);
      scoreMap.set(result.id, (scoreMap.get(result.id) || 0) + score);
    });
    
    // 计算关键词检索分数
    keyword.forEach((result, index) => {
      const score = 1 / (k + index + 1);
      scoreMap.set(result.id, (scoreMap.get(result.id) || 0) + score);
    });
    
    // 合并并排序
    const allResults = [...semantic, ...keyword];
    const uniqueResults = Array.from(new Map(allResults.map(r => [r.id, r])).values());
    
    return uniqueResults
      .map(result => ({
        ...result,
        score: scoreMap.get(result.id) || 0
      }))
      .sort((a, b) => b.score - a.score);
  }
}
```

## 5. 生成流程

### 5.1 上下文构建阶段
```mermaid
sequenceDiagram
    participant RetrievalService as 检索服务
    participant GenerationService as 生成服务
    participant PromptEngine as 提示词引擎
    participant LLM as 大语言模型
    participant Cache as Redis缓存
    participant DB as PostgreSQL

    RetrievalService->>GenerationService: 发送检索结果
    GenerationService->>PromptEngine: 构建提示词
    
    PromptEngine->>PromptEngine: 选择提示词模板
    PromptEngine->>PromptEngine: 注入检索上下文
    PromptEngine->>PromptEngine: 格式化用户问题
    PromptEngine-->>GenerationService: 返回完整提示词
    
    GenerationService->>Cache: 检查缓存
    alt 缓存命中
        Cache-->>GenerationService: 返回缓存结果
    else 缓存未命中
        GenerationService->>LLM: 调用生成模型
        LLM-->>GenerationService: 返回生成结果
        GenerationService->>Cache: 缓存结果
    end
    
    GenerationService->>DB: 保存对话记录
    GenerationService-->>RetrievalService: 返回最终答案
```

#### 提示词模板
```typescript
// 提示词模板
interface PromptTemplate {
  id: string;                           // 模板ID
  name: string;                         // 模板名称
  template: string;                     // 模板内容
  variables: string[];                  // 变量列表
  model: string;                        // 适用模型
  maxTokens: number;                    // 最大token数
  temperature: number;                  // 生成温度
}

// 默认问答模板
const QA_TEMPLATE = `
你是一个专业的AI助手，请基于以下提供的上下文信息回答用户的问题。

上下文信息：
{context}

用户问题：{question}

请遵循以下要求：
1. 仅基于提供的上下文信息回答问题
2. 如果上下文中没有相关信息，请明确说明
3. 回答要准确、简洁、有条理
4. 如果可能，请引用具体的信息来源

回答：
`;
```

### 5.2 流式生成处理
```typescript
// 流式生成服务
class StreamingGenerationService {
  async generateStream(
    prompt: string,
    config: GenerationConfig
  ): Promise<AsyncIterable<string>> {
    const stream = await this.llmClient.createChatCompletionStream({
      model: config.model,
      messages: [{ role: 'user', content: prompt }],
      temperature: config.temperature,
      max_tokens: config.maxTokens,
      stream: true
    });

    return this.processStream(stream);
  }

  private async* processStream(stream: any): AsyncIterable<string> {
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        yield content;
      }
    }
  }
}
```

## 6. 数据一致性和事务管理

### 6.1 分布式事务
```typescript
// 分布式事务管理
class DistributedTransaction {
  async executeDocumentProcessing(documentId: string): Promise<void> {
    const transaction = await this.beginTransaction();
    
    try {
      // 1. 更新文档状态
      await this.documentService.updateStatus(documentId, 'processing', transaction);
      
      // 2. 解析文档
      const chunks = await this.documentService.parseDocument(documentId, transaction);
      
      // 3. 生成向量
      const vectors = await this.embeddingService.generateVectors(chunks, transaction);
      
      // 4. 存储向量
      await this.vectorService.storeVectors(vectors, transaction);
      
      // 5. 更新完成状态
      await this.documentService.updateStatus(documentId, 'completed', transaction);
      
      await this.commitTransaction(transaction);
    } catch (error) {
      await this.rollbackTransaction(transaction);
      throw error;
    }
  }
}
```

### 6.2 数据同步策略
```typescript
// 数据同步配置
interface SyncConfig {
  strategy: 'immediate' | 'batch' | 'scheduled';
  batchSize: number;
  syncInterval: number;
  retryPolicy: {
    maxRetries: number;
    backoffMultiplier: number;
    maxBackoffTime: number;
  };
}

// 向量数据同步
class VectorSyncService {
  async syncVectors(config: SyncConfig): Promise<void> {
    switch (config.strategy) {
      case 'immediate':
        await this.immediateSync();
        break;
      case 'batch':
        await this.batchSync(config.batchSize);
        break;
      case 'scheduled':
        await this.scheduledSync(config.syncInterval);
        break;
    }
  }
}
```

## 7. 性能优化策略

### 7.1 缓存策略
- **查询缓存**：缓存常见查询的检索结果
- **向量缓存**：缓存计算好的文本向量
- **生成缓存**：缓存LLM生成的答案
- **元数据缓存**：缓存文档元数据信息

### 7.2 批处理优化
- **批量向量化**：一次处理多个文本块
- **批量检索**：并行处理多个查询
- **批量存储**：批量写入数据库
- **批量更新**：批量更新索引

### 7.3 异步处理
- **消息队列**：异步处理耗时任务
- **事件驱动**：基于事件的异步通信
- **流式处理**：实时数据流处理
- **后台任务**：定时任务和维护作业
