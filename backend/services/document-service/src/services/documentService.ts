/**
 * 文档服务
 * 处理文档相关的数据库操作
 */

import { query } from '../config/database';
import { logger } from '../utils/logger';
import { AppError } from '../utils/appError';

export interface Document {
  id: string;
  userId: string;
  filename: string;
  title: string;
  description?: string;
  fileType: string;
  fileSize: number;
  fileHash: string;
  storagePath: string;
  status: string;
  processingProgress: number;
  processingLog: any[];
  tags: string[];
  category?: string;
  metadata: any;
  uploadedAt: Date;
  processedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateDocumentData {
  userId: string;
  filename: string;
  title: string;
  description?: string;
  fileType: string;
  fileSize: number;
  fileHash: string;
  storagePath: string;
  tags?: string[];
  category?: string;
  metadata?: any;
}

export interface UpdateDocumentData {
  title?: string;
  description?: string;
  status?: string;
  processingProgress?: number;
  processingLog?: any[];
  tags?: string[];
  category?: string;
  metadata?: any;
  processedAt?: Date;
}

export class DocumentService {
  /**
   * 创建文档记录
   */
  async create(documentData: CreateDocumentData): Promise<Document> {
    try {
      const result = await query(
        `INSERT INTO documents (
          user_id, filename, title, description, file_type, file_size, 
          file_hash, storage_path, tags, category, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *`,
        [
          documentData.userId,
          documentData.filename,
          documentData.title,
          documentData.description,
          documentData.fileType,
          documentData.fileSize,
          documentData.fileHash,
          documentData.storagePath,
          documentData.tags || [],
          documentData.category,
          JSON.stringify(documentData.metadata || {})
        ]
      );

      const document = result.rows[0];
      logger.info('文档创建成功:', { documentId: document.id, filename: document.filename });
      
      return document;
    } catch (error) {
      logger.error('创建文档失败:', { filename: documentData.filename, error: error.message });
      
      if (error.code === '23505') { // 唯一约束违反
        throw new AppError('文档已存在', 400, 'DOCUMENT_ALREADY_EXISTS');
      }
      
      throw new AppError('创建文档失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 根据ID查找文档
   */
  async findById(id: string): Promise<Document | null> {
    try {
      const result = await query(
        'SELECT * FROM documents WHERE id = $1',
        [id]
      );
      
      return result.rows[0] || null;
    } catch (error) {
      logger.error('查找文档失败:', { id, error: error.message });
      throw new AppError('查找文档失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 根据文件哈希查找文档
   */
  async findByHash(fileHash: string): Promise<Document | null> {
    try {
      const result = await query(
        'SELECT * FROM documents WHERE file_hash = $1',
        [fileHash]
      );
      
      return result.rows[0] || null;
    } catch (error) {
      logger.error('根据哈希查找文档失败:', { fileHash, error: error.message });
      throw new AppError('查找文档失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 更新文档信息
   */
  async update(id: string, documentData: UpdateDocumentData): Promise<Document> {
    try {
      const setParts: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      // 动态构建更新字段
      if (documentData.title !== undefined) {
        setParts.push(`title = $${paramIndex++}`);
        values.push(documentData.title);
      }
      
      if (documentData.description !== undefined) {
        setParts.push(`description = $${paramIndex++}`);
        values.push(documentData.description);
      }
      
      if (documentData.status !== undefined) {
        setParts.push(`status = $${paramIndex++}`);
        values.push(documentData.status);
      }
      
      if (documentData.processingProgress !== undefined) {
        setParts.push(`processing_progress = $${paramIndex++}`);
        values.push(documentData.processingProgress);
      }
      
      if (documentData.processingLog !== undefined) {
        setParts.push(`processing_log = $${paramIndex++}`);
        values.push(JSON.stringify(documentData.processingLog));
      }
      
      if (documentData.tags !== undefined) {
        setParts.push(`tags = $${paramIndex++}`);
        values.push(documentData.tags);
      }
      
      if (documentData.category !== undefined) {
        setParts.push(`category = $${paramIndex++}`);
        values.push(documentData.category);
      }
      
      if (documentData.metadata !== undefined) {
        setParts.push(`metadata = $${paramIndex++}`);
        values.push(JSON.stringify(documentData.metadata));
      }
      
      if (documentData.processedAt !== undefined) {
        setParts.push(`processed_at = $${paramIndex++}`);
        values.push(documentData.processedAt);
      }

      if (setParts.length === 0) {
        throw new AppError('没有提供更新字段', 400, 'NO_UPDATE_FIELDS');
      }

      // 添加更新时间
      setParts.push(`updated_at = CURRENT_TIMESTAMP`);
      values.push(id);

      const result = await query(
        `UPDATE documents SET ${setParts.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
        values
      );

      if (result.rows.length === 0) {
        throw new AppError('文档不存在', 404, 'DOCUMENT_NOT_FOUND');
      }

      const document = result.rows[0];
      logger.info('文档更新成功:', { documentId: id, fields: Object.keys(documentData) });
      
      return document;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      
      logger.error('更新文档失败:', { id, error: error.message });
      throw new AppError('更新文档失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 删除文档
   */
  async delete(id: string): Promise<void> {
    try {
      const result = await query(
        'DELETE FROM documents WHERE id = $1',
        [id]
      );

      if (result.rowCount === 0) {
        throw new AppError('文档不存在', 404, 'DOCUMENT_NOT_FOUND');
      }

      logger.info('文档删除成功:', { documentId: id });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      
      logger.error('删除文档失败:', { id, error: error.message });
      throw new AppError('删除文档失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 获取用户文档列表
   */
  async findByUser(
    userId: string,
    options: {
      page?: number;
      pageSize?: number;
      status?: string;
      category?: string;
      search?: string;
      tags?: string[];
    } = {}
  ): Promise<{ documents: Document[]; total: number }> {
    try {
      const {
        page = 1,
        pageSize = 20,
        status,
        category,
        search,
        tags
      } = options;

      const offset = (page - 1) * pageSize;
      const conditions: string[] = ['user_id = $1'];
      const values: any[] = [userId];
      let paramIndex = 2;

      // 构建查询条件
      if (status) {
        conditions.push(`status = $${paramIndex++}`);
        values.push(status);
      }

      if (category) {
        conditions.push(`category = $${paramIndex++}`);
        values.push(category);
      }

      if (search) {
        conditions.push(`(title ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`);
        values.push(`%${search}%`);
        paramIndex++;
      }

      if (tags && tags.length > 0) {
        conditions.push(`tags && $${paramIndex++}`);
        values.push(tags);
      }

      const whereClause = `WHERE ${conditions.join(' AND ')}`;

      // 查询总数
      const countResult = await query(
        `SELECT COUNT(*) FROM documents ${whereClause}`,
        values
      );
      const total = parseInt(countResult.rows[0].count);

      // 查询文档列表
      values.push(pageSize, offset);
      const documentsResult = await query(
        `SELECT * FROM documents ${whereClause}
         ORDER BY created_at DESC
         LIMIT $${paramIndex++} OFFSET $${paramIndex}`,
        values
      );

      return {
        documents: documentsResult.rows,
        total
      };
    } catch (error) {
      logger.error('查询用户文档列表失败:', { userId, options, error: error.message });
      throw new AppError('查询文档列表失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 获取文档统计
   */
  async getStats(userId?: string): Promise<{
    total: number;
    processing: number;
    completed: number;
    failed: number;
    totalSize: number;
  }> {
    try {
      const conditions = userId ? 'WHERE user_id = $1' : '';
      const values = userId ? [userId] : [];

      const result = await query(`
        SELECT 
          COUNT(*) as total,
          COUNT(*) FILTER (WHERE status = 'processing') as processing,
          COUNT(*) FILTER (WHERE status = 'completed') as completed,
          COUNT(*) FILTER (WHERE status = 'failed') as failed,
          COALESCE(SUM(file_size), 0) as total_size
        FROM documents ${conditions}
      `, values);

      const stats = result.rows[0];
      return {
        total: parseInt(stats.total),
        processing: parseInt(stats.processing),
        completed: parseInt(stats.completed),
        failed: parseInt(stats.failed),
        totalSize: parseInt(stats.total_size)
      };
    } catch (error) {
      logger.error('获取文档统计失败:', { userId, error: error.message });
      throw new AppError('获取文档统计失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 添加处理日志
   */
  async addProcessingLog(id: string, logEntry: any): Promise<void> {
    try {
      await query(
        `UPDATE documents 
         SET processing_log = processing_log || $1::jsonb,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = $2`,
        [JSON.stringify([logEntry]), id]
      );

      logger.debug('添加处理日志成功:', { documentId: id });
    } catch (error) {
      logger.error('添加处理日志失败:', { id, error: error.message });
      // 这个错误不应该阻止主要流程
    }
  }

  /**
   * 批量更新文档状态
   */
  async batchUpdateStatus(ids: string[], status: string): Promise<void> {
    try {
      await query(
        `UPDATE documents 
         SET status = $1, updated_at = CURRENT_TIMESTAMP
         WHERE id = ANY($2)`,
        [status, ids]
      );

      logger.info('批量更新文档状态成功:', { count: ids.length, status });
    } catch (error) {
      logger.error('批量更新文档状态失败:', { ids, status, error: error.message });
      throw new AppError('批量更新文档状态失败', 500, 'DATABASE_ERROR');
    }
  }
}
