/**
 * MinIO对象存储配置
 * 用于存储原始文档文件
 */

import { Client } from 'minio';
import { logger } from '../utils/logger';

let minioClient: Client;

/**
 * MinIO连接配置
 */
const minioConfig = {
  endPoint: process.env.MINIO_ENDPOINT?.split(':')[0] || 'localhost',
  port: parseInt(process.env.MINIO_ENDPOINT?.split(':')[1] || '9000'),
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
  secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin123',
};

const bucketName = process.env.MINIO_BUCKET_NAME || 'rag-documents';

/**
 * 连接MinIO
 */
export async function connectMinIO(): Promise<void> {
  try {
    minioClient = new Client(minioConfig);
    
    // 测试连接
    await minioClient.listBuckets();
    
    // 确保存储桶存在
    const bucketExists = await minioClient.bucketExists(bucketName);
    if (!bucketExists) {
      await minioClient.makeBucket(bucketName, 'us-east-1');
      logger.info(`创建存储桶成功: ${bucketName}`);
    }
    
    logger.info('MinIO连接成功');
  } catch (error) {
    logger.error('MinIO连接失败:', error);
    throw error;
  }
}

/**
 * 获取MinIO客户端
 */
export function getMinIOClient(): Client {
  if (!minioClient) {
    throw new Error('MinIO未初始化，请先调用connectMinIO()');
  }
  return minioClient;
}

/**
 * 上传文件
 */
export async function uploadFile(
  objectName: string,
  filePath: string,
  metaData?: Record<string, string>
): Promise<string> {
  try {
    const etag = await minioClient.fPutObject(bucketName, objectName, filePath, metaData);
    logger.info('文件上传成功:', { objectName, etag });
    return etag;
  } catch (error) {
    logger.error('文件上传失败:', { objectName, error: error.message });
    throw error;
  }
}

/**
 * 上传文件流
 */
export async function uploadStream(
  objectName: string,
  stream: NodeJS.ReadableStream,
  size?: number,
  metaData?: Record<string, string>
): Promise<string> {
  try {
    const etag = await minioClient.putObject(bucketName, objectName, stream, size, metaData);
    logger.info('文件流上传成功:', { objectName, etag });
    return etag;
  } catch (error) {
    logger.error('文件流上传失败:', { objectName, error: error.message });
    throw error;
  }
}

/**
 * 下载文件
 */
export async function downloadFile(objectName: string): Promise<NodeJS.ReadableStream> {
  try {
    const stream = await minioClient.getObject(bucketName, objectName);
    logger.debug('文件下载成功:', { objectName });
    return stream;
  } catch (error) {
    logger.error('文件下载失败:', { objectName, error: error.message });
    throw error;
  }
}

/**
 * 获取文件信息
 */
export async function getFileInfo(objectName: string): Promise<any> {
  try {
    const stat = await minioClient.statObject(bucketName, objectName);
    logger.debug('获取文件信息成功:', { objectName });
    return stat;
  } catch (error) {
    logger.error('获取文件信息失败:', { objectName, error: error.message });
    throw error;
  }
}

/**
 * 删除文件
 */
export async function deleteFile(objectName: string): Promise<void> {
  try {
    await minioClient.removeObject(bucketName, objectName);
    logger.info('文件删除成功:', { objectName });
  } catch (error) {
    logger.error('文件删除失败:', { objectName, error: error.message });
    throw error;
  }
}

/**
 * 批量删除文件
 */
export async function deleteFiles(objectNames: string[]): Promise<void> {
  try {
    await minioClient.removeObjects(bucketName, objectNames);
    logger.info('批量删除文件成功:', { count: objectNames.length });
  } catch (error) {
    logger.error('批量删除文件失败:', { error: error.message });
    throw error;
  }
}

/**
 * 生成预签名URL
 */
export async function generatePresignedUrl(
  objectName: string,
  expiry: number = 7 * 24 * 60 * 60 // 7天
): Promise<string> {
  try {
    const url = await minioClient.presignedGetObject(bucketName, objectName, expiry);
    logger.debug('生成预签名URL成功:', { objectName, expiry });
    return url;
  } catch (error) {
    logger.error('生成预签名URL失败:', { objectName, error: error.message });
    throw error;
  }
}

/**
 * 生成上传预签名URL
 */
export async function generateUploadPresignedUrl(
  objectName: string,
  expiry: number = 60 * 60 // 1小时
): Promise<string> {
  try {
    const url = await minioClient.presignedPutObject(bucketName, objectName, expiry);
    logger.debug('生成上传预签名URL成功:', { objectName, expiry });
    return url;
  } catch (error) {
    logger.error('生成上传预签名URL失败:', { objectName, error: error.message });
    throw error;
  }
}

/**
 * 列出文件
 */
export async function listFiles(prefix?: string, recursive: boolean = false): Promise<any[]> {
  try {
    const objects: any[] = [];
    const stream = minioClient.listObjects(bucketName, prefix, recursive);
    
    return new Promise((resolve, reject) => {
      stream.on('data', (obj) => objects.push(obj));
      stream.on('error', reject);
      stream.on('end', () => {
        logger.debug('列出文件成功:', { count: objects.length, prefix });
        resolve(objects);
      });
    });
  } catch (error) {
    logger.error('列出文件失败:', { prefix, error: error.message });
    throw error;
  }
}

/**
 * 检查文件是否存在
 */
export async function fileExists(objectName: string): Promise<boolean> {
  try {
    await minioClient.statObject(bucketName, objectName);
    return true;
  } catch (error) {
    if (error.code === 'NotFound') {
      return false;
    }
    throw error;
  }
}

/**
 * 获取存储桶名称
 */
export function getBucketName(): string {
  return bucketName;
}
