{"name": "document-service", "version": "1.0.0", "description": "RAG系统文档服务 - 负责文档上传、解析、预处理和分块", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["document-service", "file-upload", "document-parsing", "text-processing"], "author": "RAG Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "minio": "^7.1.3", "redis": "^4.6.10", "pg": "^8.11.3", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "uuid": "^9.0.1", "crypto": "^1.0.1", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "xlsx": "^0.18.5", "cheerio": "^1.0.0-rc.12", "marked": "^11.1.1", "mime-types": "^2.1.35", "sharp": "^0.33.1", "bull": "^4.12.2", "ioredis": "^5.3.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/multer": "^1.4.11", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@types/pdf-parse": "^1.1.4", "@types/mime-types": "^2.1.4", "@types/marked": "^6.0.0", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}, "engines": {"node": ">=18.0.0"}}